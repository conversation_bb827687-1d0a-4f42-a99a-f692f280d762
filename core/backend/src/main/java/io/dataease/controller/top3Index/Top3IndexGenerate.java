package io.dataease.controller.top3Index;

import com.github.xiaoymin.knife4j.annotations.ApiSupport;
import io.dataease.plugins.common.dto.top3Index.Top3IndexInfoDTO;
import io.dataease.service.chart.Top3IndexGenerateService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 医院三甲指标生成
 */
@RestController
@Api(tags = "三甲评审指标：生成")
@ApiSupport(order = 220)
@RequestMapping("/top3index")
public class Top3IndexGenerate {

    @Resource
    private Top3IndexGenerateService top3IndexGenerateService;

    @ApiOperation("生成仪表板_根据指标明细")
    @PostMapping("/indexGenerate")
    public Boolean indexGenerate(@RequestBody List<Top3IndexInfoDTO> top3IndexSyncTasks) {
        return top3IndexGenerateService.top3IndexBatchGenerate(top3IndexSyncTasks);
    }
}
